"use client";

import { Badge } from "@midday/ui/badge";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@midday/ui/card";
import { Progress } from "@midday/ui/progress";
import { formatDistanceToNow, isValid } from "date-fns";
import { Building2, Calendar, DollarSign, MapPin, Users } from "lucide-react";
import type { RouterOutputs } from "@api/trpc/routers/_app";

type ConstructionProject = RouterOutputs["constructionProjects"]["get"]["data"][0];

interface Props {
  project: ConstructionProject;
  onClick?: () => void;
}

const statusColors = {
  planning: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  in_progress: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  on_hold: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const phaseLabels = {
  site_preparation: "Site Preparation",
  foundation: "Foundation",
  framing: "Framing",
  roofing: "Roofing",
  electrical: "Electrical",
  plumbing: "Plumbing",
  insulation: "Insulation",
  drywall: "Drywall",
  flooring: "Flooring",
  painting: "Painting",
  final_inspection: "Final Inspection",
};

const statusLabels = {
  planning: "Planning",
  in_progress: "In Progress",
  on_hold: "On Hold",
  completed: "Completed",
  cancelled: "Cancelled",
};

export function ConstructionProjectCard({ project, onClick }: Props) {
  const completion = project.completionPercentage || 0;
  const budgetUsed = project.actualCost && project.estimatedCost 
    ? (project.actualCost / project.estimatedCost) * 100 
    : 0;

  const isOverBudget = budgetUsed > 100;
  const isOnTrack = completion >= 80 && !isOverBudget;

  return (
    <Card 
      className="cursor-pointer transition-all hover:shadow-md" 
      onClick={onClick}
    >
      <CardHeader className="space-y-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{project.name}</CardTitle>
          <Badge 
            variant="secondary" 
            className={statusColors[project.status as keyof typeof statusColors]}
          >
            {statusLabels[project.status as keyof typeof statusLabels]}
          </Badge>
        </div>
        
        {project.location && (
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="mr-1 h-3 w-3" />
            {project.location}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{completion.toFixed(1)}%</span>
          </div>
          <Progress value={completion} className="h-2" />
        </div>

        {/* Current Phase */}
        {project.currentPhase && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Current Phase</span>
            <Badge variant="outline">
              {phaseLabels[project.currentPhase as keyof typeof phaseLabels]}
            </Badge>
          </div>
        )}

        {/* Financial Information */}
        {project.estimatedCost && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-muted-foreground">
                <DollarSign className="mr-1 h-3 w-3" />
                Budget
              </div>
              <div className="text-right">
                <div className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: project.currency || 'USD',
                  }).format(project.estimatedCost)}
                </div>
                {project.actualCost && (
                  <div className={`text-xs ${isOverBudget ? 'text-red-600' : 'text-muted-foreground'}`}>
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: project.currency || 'USD',
                    }).format(project.actualCost)} spent
                  </div>
                )}
              </div>
            </div>
            
            {project.actualCost && (
              <Progress 
                value={Math.min(budgetUsed, 100)} 
                className={`h-1 ${isOverBudget ? '[&>div]:bg-red-500' : ''}`}
              />
            )}
          </div>
        )}

        {/* Timeline */}
        {(project.startDate || project.endDate) && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center text-muted-foreground">
              <Calendar className="mr-1 h-3 w-3" />
              Timeline
            </div>
            <div className="text-right">
              {project.startDate && (() => {
                const startDate = new Date(project.startDate);
                return isValid(startDate) ? (
                  <div className="text-xs text-muted-foreground">
                    Started {formatDistanceToNow(startDate)} ago
                  </div>
                ) : null;
              })()}
              {project.endDate && (() => {
                const endDate = new Date(project.endDate);
                return isValid(endDate) ? (
                  <div className="text-xs">
                    Due {formatDistanceToNow(endDate)}
                  </div>
                ) : null;
              })()}
            </div>
          </div>
        )}

        {/* Project Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          {project.siteArea && (
            <div>
              <div className="text-muted-foreground">Site Area</div>
              <div className="font-medium">
                {new Intl.NumberFormat().format(project.siteArea)} sq ft
              </div>
            </div>
          )}
          
          {project.buildingArea && (
            <div>
              <div className="text-muted-foreground">Building Area</div>
              <div className="font-medium">
                {new Intl.NumberFormat().format(project.buildingArea)} sq ft
              </div>
            </div>
          )}
        </div>

        {/* Customer */}
        {project.customer && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center text-muted-foreground">
              <Users className="mr-1 h-3 w-3" />
              Client
            </div>
            <div className="font-medium">{project.customer.name}</div>
          </div>
        )}

        {/* Description */}
        {project.description && (
          <div className="text-sm text-muted-foreground line-clamp-2">
            {project.description}
          </div>
        )}

        {/* Status indicators */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-2">
            {isOnTrack && (
              <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                On Track
              </Badge>
            )}
            {isOverBudget && (
              <Badge variant="secondary" className="text-xs bg-red-100 text-red-800">
                Over Budget
              </Badge>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            {(() => {
              const updateDate = new Date(project.updated_at || project.created_at);
              return isValid(updateDate)
                ? `Updated ${formatDistanceToNow(updateDate)} ago`
                : 'Recently updated';
            })()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}