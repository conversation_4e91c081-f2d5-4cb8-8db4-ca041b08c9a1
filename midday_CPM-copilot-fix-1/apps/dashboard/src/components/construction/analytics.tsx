"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@midday/ui/tabs";
import { Progress } from "@midday/ui/progress";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, Responsive<PERSON>ontainer, <PERSON><PERSON>hart, Line } from "recharts";
import { useState } from "react";

interface AnalyticsProps {
  project: any;
  siteMeasurements: any[];
}

// Mock data for charts
const progressData = [
  { day: 1, progress: 10 },
  { day: 2, progress: 15 },
  { day: 3, progress: 25 },
  { day: 4, progress: 30 },
  { day: 5, progress: 35 },
  { day: 6, progress: 45 },
  { day: 7, progress: 50 },
  { day: 8, progress: 55 },
  { day: 9, progress: 60 },
  { day: 10, progress: 66 },
];

const workAreasData = [
  { area: "Foundation", progress: 85, color: "#f59e0b" },
  { area: "Framing", progress: 60, color: "#ef4444" },
  { area: "Electrical", progress: 40, color: "#3b82f6" },
  { area: "Plumbing", progress: 25, color: "#10b981" },
];

export function ConstructionAnalytics({ project, siteMeasurements }: AnalyticsProps) {
  const [activeTab, setActiveTab] = useState("progress");

  const completion = project.completionPercentage || 66;
  const latestMeasurement = siteMeasurements?.[0];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="daily">Daily progress chart</TabsTrigger>
          <TabsTrigger value="work-areas">Work areas</TabsTrigger>
        </TabsList>

        <div className="flex-1 p-4">
          <TabsContent value="timeline" className="h-full">
            <div className="text-center text-muted-foreground">
              Timeline view coming soon...
            </div>
          </TabsContent>

          <TabsContent value="progress" className="h-full">
            <div className="grid grid-cols-2 gap-8 h-full">
              {/* Left Side - Circular Progress */}
              <div className="flex items-center justify-center">
                <div className="relative">
                  {/* Large circular progress indicator */}
                  <div className="w-48 h-48 relative">
                    <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#e5e7eb"
                        strokeWidth="8"
                        fill="none"
                      />
                      {/* Progress circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#f59e0b"
                        strokeWidth="8"
                        fill="none"
                        strokeDasharray={`${2 * Math.PI * 40}`}
                        strokeDashoffset={`${2 * Math.PI * 40 * (1 - completion / 100)}`}
                        strokeLinecap="round"
                        className="transition-all duration-500"
                      />
                    </svg>
                    
                    {/* Center text */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-orange-500">{completion}%</div>
                        <div className="text-sm text-muted-foreground">Complete</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Volume Data */}
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Remaining</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Fill:</span>
                        <span className="font-medium">15,837 cu yd</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cut:</span>
                        <span className="font-medium">68,727 cu yd</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Total</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Fill:</span>
                        <span className="font-medium">75,464 cu yd</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cut:</span>
                        <span className="font-medium">174,732 cu yd</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Progress Bars */}
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Fill Progress</span>
                      <span>79%</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-orange-500 rounded-full" style={{ width: "79%" }} />
                    </div>
                    
                    {/* Mini bar chart for fill */}
                    <div className="flex items-end gap-1 h-8 mt-2">
                      {[60, 65, 70, 75, 80, 85, 79, 75, 70, 65, 60, 55].map((height, i) => (
                        <div
                          key={i}
                          className="bg-orange-400 rounded-sm flex-1"
                          style={{ height: `${height}%` }}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Cut Progress</span>
                      <span>61%</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-red-500 rounded-full" style={{ width: "61%" }} />
                    </div>
                    
                    {/* Mini bar chart for cut */}
                    <div className="flex items-end gap-1 h-8 mt-2">
                      {[45, 50, 55, 60, 65, 70, 61, 58, 55, 52, 48, 45].map((height, i) => (
                        <div
                          key={i}
                          className="bg-red-400 rounded-sm flex-1"
                          style={{ height: `${height}%` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="daily" className="h-full">
            <div className="h-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={progressData}>
                  <XAxis 
                    dataKey="day" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="progress" 
                    stroke="#f59e0b" 
                    strokeWidth={3}
                    dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#f59e0b' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="work-areas" className="h-full">
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {workAreasData.map((area) => (
                  <div key={area.area} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{area.area}</span>
                      <span className="font-medium">{area.progress}%</span>
                    </div>
                    <Progress 
                      value={area.progress} 
                      className="h-2"
                      style={{ 
                        '--progress-background': area.color 
                      } as React.CSSProperties}
                    />
                  </div>
                ))}
              </div>
              
              {/* Work Areas Chart */}
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={workAreasData}>
                    <XAxis 
                      dataKey="area" 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                    />
                    <Bar 
                      dataKey="progress" 
                      fill="#f59e0b"
                      radius={[2, 2, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
