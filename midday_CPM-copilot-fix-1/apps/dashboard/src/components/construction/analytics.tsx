"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@midday/ui/tabs";
import { Progress } from "@midday/ui/progress";
import { Badge } from "@midday/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@midday/ui/card";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Tooltip } from "recharts";
import { Calendar, TrendingUp, AlertTriangle, Users, Clock } from "lucide-react";
import { useState } from "react";

interface AnalyticsProps {
  project: any;
  siteMeasurements: any[];
}

// Mock data for charts
const progressData = [
  { day: 1, progress: 10 },
  { day: 2, progress: 15 },
  { day: 3, progress: 25 },
  { day: 4, progress: 30 },
  { day: 5, progress: 35 },
  { day: 6, progress: 45 },
  { day: 7, progress: 50 },
  { day: 8, progress: 55 },
  { day: 9, progress: 60 },
  { day: 10, progress: 66 },
];

const workAreasData = [
  { area: "Foundation", progress: 85, color: "#f59e0b", workers: 12, status: "on_track" },
  { area: "Framing", progress: 60, color: "#ef4444", workers: 8, status: "delayed" },
  { area: "Electrical", progress: 40, color: "#3b82f6", workers: 6, status: "on_track" },
  { area: "Plumbing", progress: 25, color: "#10b981", workers: 4, status: "ahead" },
];

// Timeline data for Gantt-style view
const timelineData = [
  { phase: "Site Prep", start: "2024-01-15", end: "2024-02-15", progress: 100, status: "completed" },
  { phase: "Foundation", start: "2024-02-01", end: "2024-03-15", progress: 85, status: "in_progress" },
  { phase: "Framing", start: "2024-03-01", end: "2024-04-30", progress: 60, status: "in_progress" },
  { phase: "Electrical", start: "2024-04-15", end: "2024-06-15", progress: 40, status: "in_progress" },
  { phase: "Plumbing", start: "2024-04-15", end: "2024-06-30", progress: 25, status: "scheduled" },
  { phase: "Finishing", start: "2024-06-01", end: "2024-08-15", progress: 0, status: "scheduled" },
];

// Resource allocation data
const resourceData = [
  { name: "Workers", value: 24, color: "#f59e0b" },
  { name: "Equipment", value: 8, color: "#3b82f6" },
  { name: "Materials", value: 12, color: "#10b981" },
  { name: "Subcontractors", value: 6, color: "#ef4444" },
];

export function ConstructionAnalytics({ project, siteMeasurements }: AnalyticsProps) {
  const [activeTab, setActiveTab] = useState("progress");

  const completion = project.completionPercentage || 66;
  const latestMeasurement = siteMeasurements?.[0];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-5 mx-4 mt-4">
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="daily">Daily Chart</TabsTrigger>
          <TabsTrigger value="work-areas">Work Areas</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        <div className="flex-1 p-4">
          <TabsContent value="timeline" className="h-full">
            <div className="space-y-4">
              {/* Timeline Header */}
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Project Timeline</h3>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Jan 2024 - Aug 2024</span>
                </div>
              </div>

              {/* Timeline Items */}
              <div className="space-y-3">
                {timelineData.map((phase, index) => (
                  <Card key={phase.phase} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <div className="text-sm font-medium">{phase.phase}</div>
                        <Badge
                          variant={
                            phase.status === "completed" ? "default" :
                            phase.status === "in_progress" ? "secondary" :
                            "outline"
                          }
                          className={
                            phase.status === "completed" ? "bg-green-100 text-green-800" :
                            phase.status === "in_progress" ? "bg-orange-100 text-orange-800" :
                            "bg-gray-100 text-gray-800"
                          }
                        >
                          {phase.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {phase.progress}%
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                      <span>{phase.start}</span>
                      <span>→</span>
                      <span>{phase.end}</span>
                    </div>

                    <Progress value={phase.progress} className="h-2" />
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="progress" className="h-full">
            <div className="grid grid-cols-2 gap-8 h-full">
              {/* Left Side - Circular Progress */}
              <div className="flex items-center justify-center">
                <div className="relative">
                  {/* Large circular progress indicator */}
                  <div className="w-48 h-48 relative">
                    <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#e5e7eb"
                        strokeWidth="8"
                        fill="none"
                      />
                      {/* Progress circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#f59e0b"
                        strokeWidth="8"
                        fill="none"
                        strokeDasharray={`${2 * Math.PI * 40}`}
                        strokeDashoffset={`${2 * Math.PI * 40 * (1 - completion / 100)}`}
                        strokeLinecap="round"
                        className="transition-all duration-500"
                      />
                    </svg>
                    
                    {/* Center text */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-orange-500">{completion}%</div>
                        <div className="text-sm text-muted-foreground">Complete</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Volume Data */}
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Remaining</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Fill:</span>
                        <span className="font-medium">15,837 cu yd</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cut:</span>
                        <span className="font-medium">68,727 cu yd</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-muted-foreground mb-1">Total</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Fill:</span>
                        <span className="font-medium">75,464 cu yd</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cut:</span>
                        <span className="font-medium">174,732 cu yd</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Progress Bars */}
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Fill Progress</span>
                      <span>79%</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-orange-500 rounded-full" style={{ width: "79%" }} />
                    </div>
                    
                    {/* Mini bar chart for fill */}
                    <div className="flex items-end gap-1 h-8 mt-2">
                      {[60, 65, 70, 75, 80, 85, 79, 75, 70, 65, 60, 55].map((height, i) => (
                        <div
                          key={i}
                          className="bg-orange-400 rounded-sm flex-1"
                          style={{ height: `${height}%` }}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Cut Progress</span>
                      <span>61%</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-red-500 rounded-full" style={{ width: "61%" }} />
                    </div>
                    
                    {/* Mini bar chart for cut */}
                    <div className="flex items-end gap-1 h-8 mt-2">
                      {[45, 50, 55, 60, 65, 70, 61, 58, 55, 52, 48, 45].map((height, i) => (
                        <div
                          key={i}
                          className="bg-red-400 rounded-sm flex-1"
                          style={{ height: `${height}%` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="daily" className="h-full">
            <div className="h-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={progressData}>
                  <XAxis 
                    dataKey="day" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="progress" 
                    stroke="#f59e0b" 
                    strokeWidth={3}
                    dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#f59e0b' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="work-areas" className="h-full">
            <div className="space-y-4">
              {/* Work Areas Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {workAreasData.map((area) => (
                  <Card key={area.area} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{area.area}</span>
                          <Badge
                            variant="outline"
                            className={
                              area.status === "on_track" ? "border-green-500 text-green-700" :
                              area.status === "delayed" ? "border-red-500 text-red-700" :
                              "border-blue-500 text-blue-700"
                            }
                          >
                            {area.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <span className="text-lg font-bold">{area.progress}%</span>
                      </div>

                      <Progress value={area.progress} className="h-2" />

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          <span>{area.workers} workers</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {area.status === "delayed" && <AlertTriangle className="h-3 w-3 text-red-500" />}
                          {area.status === "ahead" && <TrendingUp className="h-3 w-3 text-green-500" />}
                          <span className="capitalize">{area.status.replace('_', ' ')}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Work Areas Chart */}
              <Card className="p-4">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Progress Overview</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="h-32">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={workAreasData}>
                        <XAxis
                          dataKey="area"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#6b7280' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#6b7280' }}
                        />
                        <Tooltip />
                        <Bar
                          dataKey="progress"
                          fill="#f59e0b"
                          radius={[2, 2, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="resources" className="h-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
              {/* Resource Allocation Pie Chart */}
              <Card className="p-4">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Resource Allocation</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={resourceData}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {resourceData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Legend */}
                  <div className="grid grid-cols-2 gap-2 mt-4">
                    {resourceData.map((item) => (
                      <div key={item.name} className="flex items-center gap-2 text-sm">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: item.color }}
                        />
                        <span>{item.name}: {item.value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Resource Details */}
              <Card className="p-4">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Resource Details</CardTitle>
                </CardHeader>
                <CardContent className="p-0 space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-orange-500" />
                        <span className="font-medium">Workers</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">24</div>
                        <div className="text-xs text-muted-foreground">Active</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-blue-500 rounded" />
                        <span className="font-medium">Equipment</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">8</div>
                        <div className="text-xs text-muted-foreground">Units</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-green-500 rounded" />
                        <span className="font-medium">Materials</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">12</div>
                        <div className="text-xs text-muted-foreground">Deliveries</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-red-500 rounded" />
                        <span className="font-medium">Subcontractors</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">6</div>
                        <div className="text-xs text-muted-foreground">Teams</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
