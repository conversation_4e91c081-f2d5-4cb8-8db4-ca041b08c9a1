"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@midday/ui/avatar";
import { Badge } from "@midday/ui/badge";
import { <PERSON><PERSON> } from "@midday/ui/button";
import { Card, CardContent } from "@midday/ui/card";
import { Plus } from "lucide-react";

interface TeamPresenceProps {
  projectId: string;
  teamPresence: any[];
}

// Mock team data - in real app this would come from teamPresence prop
const mockTeamMembers = [
  {
    id: "1",
    name: "<PERSON>",
    role: "Project Manager",
    avatar: null,
    isOnline: true,
    lastSeen: new Date(),
    initials: "<PERSON><PERSON>"
  },
  {
    id: "2", 
    name: "<PERSON>",
    role: "Site Engineer",
    avatar: null,
    isOnline: true,
    lastSeen: new Date(),
    initials: "SM"
  },
  {
    id: "3",
    name: "<PERSON>", 
    role: "Contractor",
    avatar: null,
    isOnline: false,
    lastSeen: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
    initials: "M<PERSON>"
  }
];

export function ConstructionTeamPresence({ projectId, teamPresence }: TeamPresenceProps) {
  const activeMembers = mockTeamMembers.filter(member => member.isOnline);
  const totalMembers = mockTeamMembers.length;

  return (
    <Card className="w-64 bg-background/95 backdrop-blur-sm border shadow-lg">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                {activeMembers.slice(0, 3).map((member) => (
                  <div key={member.id} className="relative">
                    <Avatar className="w-8 h-8 border-2 border-white">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-blue-500 text-white">
                        {member.initials}
                      </AvatarFallback>
                    </Avatar>
                    {member.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                    )}
                  </div>
                ))}
                {totalMembers > 3 && (
                  <div className="w-8 h-8 bg-gray-200 border-2 border-white rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      +{totalMembers - 3}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Team Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Team Online</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {activeMembers.length} of {totalMembers}
              </Badge>
            </div>

            {/* Individual Members */}
            <div className="space-y-2">
              {mockTeamMembers.map((member) => (
                <div key={member.id} className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs bg-blue-500 text-white">
                        {member.initials}
                      </AvatarFallback>
                    </Avatar>
                    <div 
                      className={`absolute -bottom-0.5 -right-0.5 w-2 h-2 border border-white rounded-full ${
                        member.isOnline ? 'bg-green-500' : 'bg-gray-400'
                      }`} 
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {member.name}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {member.role}
                    </div>
                  </div>
                  
                  <div className="text-xs text-muted-foreground">
                    {member.isOnline ? (
                      <span className="text-green-600">Online</span>
                    ) : (
                      <span>15m ago</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="pt-2 border-t space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start text-xs">
              <Plus className="h-3 w-3 mr-2" />
              Invite Team Member
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
