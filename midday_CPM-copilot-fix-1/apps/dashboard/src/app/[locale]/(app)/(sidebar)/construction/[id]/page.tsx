"use client";

import { Construction3DViewer } from "@/components/construction-3d-viewer";
import { ConstructionCollaboration } from "@/components/construction-collaboration";
import { ConstructionFileManager } from "@/components/construction-file-manager";
import { ConstructionFinancialDashboard } from "@/components/construction-financial-dashboard";
import { ConstructionProjectForm } from "@/components/forms/construction-project-form";
import { ProgressUpdateForm } from "@/components/forms/progress-update-form";
import { useTRPC } from "@/trpc/client";
import { Badge } from "@midday/ui/badge";
import { Button } from "@midday/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@midday/ui/dialog";
import { Progress } from "@midday/ui/progress";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@midday/ui/tabs";
import { formatDistanceToNow } from "date-fns";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  Edit,
  MapPin,
  MessageSquare,
  Plus,
  TrendingUp,
  Users,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";

const statusColors = {
  planning: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  in_progress: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  on_hold: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const statusLabels = {
  planning: "Planning",
  in_progress: "In Progress",
  on_hold: "On Hold",
  completed: "Completed",
  cancelled: "Cancelled",
};

export default function ConstructionProjectPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isProgressDialogOpen, setIsProgressDialogOpen] = useState(false);

  const { data: project, isLoading } = useTRPC.constructionProjects.getById.useQuery({
    id: projectId,
  });

  const { data: progressUpdates } = useTRPC.constructionProjects.getProgressUpdates.useQuery({
    projectId,
    limit: 20,
  });

  const { data: annotations } = useTRPC.constructionProjects.getAnnotations.useQuery({
    projectId,
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 h-96 bg-gray-200 rounded animate-pulse" />
          <div className="h-96 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">Project not found</h3>
        <p className="text-muted-foreground mb-4">
          The construction project you're looking for doesn't exist or you don't have access to it.
        </p>
        <Button onClick={() => router.push("/construction")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Projects
        </Button>
      </div>
    );
  }

  const completion = project.completionPercentage || 0;
  const budgetUsed = project.actualCost && project.estimatedCost 
    ? (project.actualCost / project.estimatedCost) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/construction")}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Projects
            </Button>
            <span className="text-muted-foreground">/</span>
            <h1 className="text-2xl font-bold tracking-tight">{project.name}</h1>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge 
              variant="secondary" 
              className={statusColors[project.status as keyof typeof statusColors]}
            >
              {statusLabels[project.status as keyof typeof statusLabels]}
            </Badge>
            
            {project.location && (
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="mr-1 h-3 w-3" />
                {project.location}
              </div>
            )}
            
            {project.customer && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="mr-1 h-3 w-3" />
                {project.customer.name}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Dialog open={isProgressDialogOpen} onOpenChange={setIsProgressDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Progress Update
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add Progress Update</DialogTitle>
              </DialogHeader>
              <ProgressUpdateForm 
                projectId={projectId}
                onSuccess={() => setIsProgressDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Edit Construction Project</DialogTitle>
              </DialogHeader>
              <ConstructionProjectForm 
                project={project}
                onSuccess={() => setIsEditDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Progress</p>
                <p className="text-2xl font-bold">{completion.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground" />
            </div>
            <Progress value={completion} className="mt-2" />
          </CardContent>
        </Card>

        {project.estimatedCost && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Budget</p>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: project.currency || 'USD',
                      notation: 'compact',
                    }).format(project.estimatedCost)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-muted-foreground" />
              </div>
              {project.actualCost && (
                <div className="mt-2">
                  <Progress 
                    value={Math.min(budgetUsed, 100)} 
                    className={budgetUsed > 100 ? '[&>div]:bg-red-500' : ''}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {budgetUsed.toFixed(1)}% used
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Timeline</p>
                <p className="text-2xl font-bold">
                  {project.endDate 
                    ? formatDistanceToNow(new Date(project.endDate))
                    : "TBD"}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-muted-foreground" />
            </div>
            {project.endDate && (
              <p className="text-xs text-muted-foreground mt-2">
                Due date
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Updates</p>
                <p className="text-2xl font-bold">{progressUpdates?.length || 0}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Progress reports
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 3D Viewer */}
        <div className="lg:col-span-2">
          <Construction3DViewer 
            project={project}
            onAnnotationClick={(annotation) => {
              console.log("Annotation clicked:", annotation);
            }}
            onAreaSelect={(coords) => {
              console.log("Area selected:", coords);
            }}
          />
        </div>

        {/* Project Details */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {project.description && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Description</h4>
                  <p className="text-sm mt-1">{project.description}</p>
                </div>
              )}

              {project.currentPhase && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Current Phase</h4>
                  <p className="text-sm mt-1 capitalize">{project.currentPhase.replace('_', ' ')}</p>
                </div>
              )}

              {project.address && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Address</h4>
                  <p className="text-sm mt-1">{project.address}</p>
                </div>
              )}

              {(project.siteArea || project.buildingArea) && (
                <div className="grid grid-cols-2 gap-4">
                  {project.siteArea && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Site Area</h4>
                      <p className="text-sm mt-1">{project.siteArea.toLocaleString()} sq ft</p>
                    </div>
                  )}
                  {project.buildingArea && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Building Area</h4>
                      <p className="text-sm mt-1">{project.buildingArea.toLocaleString()} sq ft</p>
                    </div>
                  )}
                </div>
              )}

              {project.contractorInfo && Object.keys(project.contractorInfo).length > 0 && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Contractor</h4>
                  <div className="text-sm mt-1 space-y-1">
                    {(project.contractorInfo as any).name && (
                      <p>{(project.contractorInfo as any).name}</p>
                    )}
                    {(project.contractorInfo as any).contact && (
                      <p className="text-muted-foreground">{(project.contractorInfo as any).contact}</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => setIsProgressDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Progress Update
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Annotation
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Inspection
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Progress Updates and Activity */}
      <Tabs defaultValue="progress" className="space-y-4">
        <TabsList>
          <TabsTrigger value="progress">Progress Updates</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="space-y-4">
          {progressUpdates?.length ? (
            progressUpdates.map((update) => (
              <Card key={update.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <p className="font-medium">{update.progressPercentage}% Complete</p>
                      {update.phase && (
                        <p className="text-sm text-muted-foreground capitalize">
                          {update.phase.replace('_', ' ')} Phase
                        </p>
                      )}
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <p>{update.userName}</p>
                      <p>{formatDistanceToNow(new Date(update.createdAt))} ago</p>
                    </div>
                  </div>
                  
                  {update.description && (
                    <p className="text-sm mb-2">{update.description}</p>
                  )}
                  
                  {(update.workCompleted || update.nextSteps) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      {update.workCompleted && (
                        <div>
                          <h5 className="font-medium text-muted-foreground">Work Completed</h5>
                          <p>{update.workCompleted}</p>
                        </div>
                      )}
                      {update.nextSteps && (
                        <div>
                          <h5 className="font-medium text-muted-foreground">Next Steps</h5>
                          <p>{update.nextSteps}</p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No progress updates yet</p>
              <Button 
                className="mt-2"
                onClick={() => setIsProgressDialogOpen(true)}
              >
                Add First Update
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-4">
          <ConstructionCollaboration projectId={projectId} />
        </TabsContent>

        <TabsContent value="files">
          <ConstructionFileManager projectId={projectId} />
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <ConstructionFinancialDashboard projectId={projectId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}