"use client";

import { ConstructionSiteViewer } from "@/components/construction/site-viewer";
import { ConstructionProjectDetails } from "@/components/construction/project-details";
import { ConstructionAnalytics } from "@/components/construction/analytics";
import { ConstructionTeamPresence } from "@/components/construction/team-presence";
import { useTRPC } from "@/trpc/client";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import { Button } from "@midday/ui/button";
import { ArrowLeft } from "lucide-react";

export default function ConstructionProjectPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const { data: project, isLoading } = useTRPC.constructionProjects.getById.useQuery({
    id: projectId,
  });

  const { data: siteMeasurements } = useTRPC.siteMeasurements.getByProject.useQuery({
    projectId,
  });

  const { data: teamPresence } = useTRPC.constructionProjects.getTeamPresence.useQuery({
    projectId,
  });

  if (isLoading) {
    return (
      <div className="h-screen flex">
        {/* Left Panel Skeleton */}
        <div className="w-80 bg-background border-r p-6 space-y-4">
          <div className="h-6 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded animate-pulse" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        </div>

        {/* Main Viewer Skeleton */}
        <div className="flex-1 bg-gray-100 animate-pulse" />

        {/* Bottom Panel Skeleton */}
        <div className="absolute bottom-0 left-80 right-0 h-64 bg-background border-t p-4">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4 mb-4" />
          <div className="grid grid-cols-4 gap-4">
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Project not found</h3>
          <p className="text-muted-foreground mb-4">
            The construction project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => router.push("/construction")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Three-Panel Layout matching the UI design */}
      <div className="flex flex-1 relative">
        {/* Left Panel - Project Details */}
        <div className="w-80 bg-background border-r flex flex-col">
          <ConstructionProjectDetails
            project={project}
            siteMeasurements={siteMeasurements}
            onBack={() => router.push("/construction")}
          />
        </div>

        {/* Main Center - Site Viewer */}
        <div className="flex-1 relative bg-gray-900">
          <ConstructionSiteViewer
            project={project}
            siteMeasurements={siteMeasurements}
          />

          {/* Team Presence Overlay */}
          <div className="absolute top-4 right-4">
            <ConstructionTeamPresence
              projectId={projectId}
              teamPresence={teamPresence}
            />
          </div>
        </div>
      </div>

      {/* Bottom Panel - Analytics */}
      <div className="h-64 bg-background border-t">
        <ConstructionAnalytics
          project={project}
          siteMeasurements={siteMeasurements}
        />
      </div>
    </div>

  );
}